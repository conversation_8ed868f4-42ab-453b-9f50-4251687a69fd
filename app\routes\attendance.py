from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from datetime import datetime, date, timedelta
from app.models.models import db, ProductionLine, AttendanceRecord, DailyStatistics
from sqlalchemy import func, and_, or_

attendance = Blueprint('attendance', __name__)

@attendance.route('/')
def index():
    """出勤记录列表"""
    page = request.args.get('page', 1, type=int)
    line_id = request.args.get('line_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    per_page = 20
    
    # 构建查询
    query = AttendanceRecord.query
    
    # 筛选条件
    if line_id:
        query = query.filter(AttendanceRecord.production_line_id == line_id)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(AttendanceRecord.date >= start_date_obj)
        except ValueError:
            flash('开始日期格式错误', 'error')
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(AttendanceRecord.date <= end_date_obj)
        except ValueError:
            flash('结束日期格式错误', 'error')
    
    # 分页查询
    records = query.order_by(AttendanceRecord.date.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取生产线列表用于筛选
    production_lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.name
    ).all()
    
    return render_template('attendance/index.html',
                         records=records,
                         production_lines=production_lines,
                         current_filters={
                             'line_id': line_id,
                             'start_date': start_date,
                             'end_date': end_date
                         })

@attendance.route('/add', methods=['GET', 'POST'])
def add():
    """添加出勤记录"""
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        production_line_id = data.get('production_line_id', type=int)
        date_str = data.get('date', '').strip()
        regular_employees = data.get('regular_employees', 0, type=int)
        assistant_employees = data.get('assistant_employees', 0, type=int)
        notes = data.get('notes', '').strip()
        
        # 验证数据
        if not production_line_id:
            if request.is_json:
                return jsonify({'success': False, 'message': '请选择生产线'})
            flash('请选择生产线', 'error')
            return render_template('attendance/add.html', 
                                 production_lines=ProductionLine.query.filter_by(is_active=True).all())
        
        if not date_str:
            if request.is_json:
                return jsonify({'success': False, 'message': '请选择日期'})
            flash('请选择日期', 'error')
            return render_template('attendance/add.html',
                                 production_lines=ProductionLine.query.filter_by(is_active=True).all())
        
        try:
            record_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            if request.is_json:
                return jsonify({'success': False, 'message': '日期格式错误'})
            flash('日期格式错误', 'error')
            return render_template('attendance/add.html',
                                 production_lines=ProductionLine.query.filter_by(is_active=True).all())
        
        # 检查是否已存在记录
        existing = AttendanceRecord.query.filter_by(
            production_line_id=production_line_id,
            date=record_date
        ).first()
        
        if existing:
            if request.is_json:
                return jsonify({'success': False, 'message': '该生产线在此日期已有出勤记录'})
            flash('该生产线在此日期已有出勤记录', 'error')
            return render_template('attendance/add.html',
                                 production_lines=ProductionLine.query.filter_by(is_active=True).all())
        
        # 创建出勤记录
        total_employees = regular_employees + assistant_employees
        record = AttendanceRecord(
            production_line_id=production_line_id,
            date=record_date,
            regular_employees=regular_employees,
            assistant_employees=assistant_employees,
            total_employees=total_employees,
            notes=notes,
            created_by='系统用户'  # 这里可以替换为实际的用户系统
        )
        
        db.session.add(record)
        
        # 更新或创建每日统计
        update_daily_statistics(record_date)
        
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True,
                'message': '出勤记录添加成功',
                'data': record.to_dict()
            })
        
        flash('出勤记录添加成功', 'success')
        return redirect(url_for('attendance.index'))
    
    # GET请求
    production_lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.name
    ).all()
    
    return render_template('attendance/add.html', production_lines=production_lines)

@attendance.route('/<int:record_id>/edit', methods=['GET', 'POST'])
def edit(record_id):
    """编辑出勤记录"""
    record = AttendanceRecord.query.get_or_404(record_id)
    
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        regular_employees = data.get('regular_employees', 0, type=int)
        assistant_employees = data.get('assistant_employees', 0, type=int)
        notes = data.get('notes', '').strip()
        
        # 更新记录
        record.regular_employees = regular_employees
        record.assistant_employees = assistant_employees
        record.total_employees = regular_employees + assistant_employees
        record.notes = notes
        record.updated_at = datetime.utcnow()
        
        # 更新每日统计
        update_daily_statistics(record.date)
        
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True,
                'message': '出勤记录更新成功',
                'data': record.to_dict()
            })
        
        flash('出勤记录更新成功', 'success')
        return redirect(url_for('attendance.index'))
    
    return render_template('attendance/edit.html', record=record)

@attendance.route('/<int:record_id>/delete', methods=['POST'])
def delete(record_id):
    """删除出勤记录"""
    record = AttendanceRecord.query.get_or_404(record_id)
    record_date = record.date
    
    db.session.delete(record)
    
    # 更新每日统计
    update_daily_statistics(record_date)
    
    db.session.commit()
    
    if request.is_json:
        return jsonify({'success': True, 'message': '出勤记录删除成功'})
    
    flash('出勤记录删除成功', 'success')
    return redirect(url_for('attendance.index'))

def update_daily_statistics(target_date):
    """更新指定日期的每日统计"""
    # 计算当日统计
    daily_stats = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('total_regular'),
        func.sum(AttendanceRecord.assistant_employees).label('total_assistant'),
        func.sum(AttendanceRecord.total_employees).label('total_all'),
        func.count(AttendanceRecord.id).label('active_lines')
    ).filter(AttendanceRecord.date == target_date).first()
    
    # 查找或创建每日统计记录
    daily_record = DailyStatistics.query.filter_by(date=target_date).first()
    
    if not daily_record:
        daily_record = DailyStatistics(date=target_date)
        db.session.add(daily_record)
    
    # 更新统计数据
    daily_record.total_regular_employees = daily_stats.total_regular or 0
    daily_record.total_assistant_employees = daily_stats.total_assistant or 0
    daily_record.total_employees = daily_stats.total_all or 0
    daily_record.active_production_lines = daily_stats.active_lines or 0
    daily_record.updated_at = datetime.utcnow()

@attendance.route('/api/records')
def api_records():
    """获取出勤记录API"""
    line_id = request.args.get('line_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    limit = request.args.get('limit', 100, type=int)
    
    query = AttendanceRecord.query
    
    if line_id:
        query = query.filter(AttendanceRecord.production_line_id == line_id)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(AttendanceRecord.date >= start_date_obj)
        except ValueError:
            return jsonify({'success': False, 'message': '开始日期格式错误'})
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(AttendanceRecord.date <= end_date_obj)
        except ValueError:
            return jsonify({'success': False, 'message': '结束日期格式错误'})
    
    records = query.order_by(AttendanceRecord.date.desc()).limit(limit).all()
    
    return jsonify({
        'success': True,
        'data': [record.to_dict() for record in records]
    })
