@echo off
echo 启动生产线出勤管理系统...

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 安装依赖
echo 安装依赖包...
pip install -r requirements.txt

REM 初始化数据库
echo 初始化数据库...
set FLASK_APP=run.py
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

REM 创建示例数据
echo 创建示例数据...
flask deploy

REM 启动应用
echo 启动应用服务器...
echo 应用将在 http://localhost:5000 启动
echo 按 Ctrl+C 停止服务器
python run.py

pause
