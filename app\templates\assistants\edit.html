{% extends "base.html" %}

{% block title %}编辑辅助人员 - {{ assistant.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">编辑辅助人员</h4>
                <small class="text-muted">生产线：{{ line.name }}</small>
            </div>
            <div class="card-body">
                <form id="editAssistantForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">姓名 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   placeholder="请输入姓名" maxlength="50" value="{{ assistant.name }}">
                            <div class="invalid-feedback">
                                请输入姓名
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="employee_id" class="form-label">工号</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                   placeholder="请输入工号（可选）" maxlength="20" value="{{ assistant.employee_id or '' }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">职位</label>
                            <input type="text" class="form-control" id="position" name="position" 
                                   placeholder="请输入职位（可选）" maxlength="50" value="{{ assistant.position or '' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">联系电话</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="请输入联系电话（可选）" maxlength="20" value="{{ assistant.phone or '' }}">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('production_lines.detail', line_id=line.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('editAssistantForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // 表单验证
    if (!validateForm('editAssistantForm')) {
        return;
    }
    
    showLoading(submitBtn);
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{{ url_for("assistants.edit", assistant_id=assistant.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("production_lines.detail", line_id=line.id) }}';
        } else {
            alert('保存失败：' + data.message);
            hideLoading(submitBtn, originalText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败，请重试');
        hideLoading(submitBtn, originalText);
    });
});

// 实时验证
document.getElementById('name').addEventListener('input', function() {
    if (this.value.trim()) {
        this.classList.remove('is-invalid');
    }
});
</script>
{% endblock %}
