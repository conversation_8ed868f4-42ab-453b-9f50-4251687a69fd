import os
from app import create_app, db
from app.models.models import ProductionLine, Assistant, AttendanceRecord, DailyStatistics
from flask_migrate import upgrade

# 创建应用实例
app = create_app(os.getenv('FLASK_CONFIG') or 'default')

@app.shell_context_processor
def make_shell_context():
    """Shell上下文处理器"""
    return {
        'db': db,
        'ProductionLine': ProductionLine,
        'Assistant': Assistant,
        'AttendanceRecord': AttendanceRecord,
        'DailyStatistics': DailyStatistics
    }

@app.cli.command()
def deploy():
    """部署命令"""
    # 创建数据库表
    upgrade()
    
    # 创建示例数据
    create_sample_data()

def create_sample_data():
    """创建示例数据"""
    # 检查是否已有数据
    if ProductionLine.query.first():
        return
    
    # 创建示例生产线
    lines = [
        ProductionLine(name='生产线A', description='主要生产线，负责产品A的生产'),
        ProductionLine(name='生产线B', description='辅助生产线，负责产品B的生产'),
        ProductionLine(name='生产线C', description='新建生产线，负责产品C的生产'),
    ]
    
    for line in lines:
        db.session.add(line)
    
    db.session.commit()
    
    # 为每个生产线添加辅助人员
    assistants_data = [
        {'name': '张三', 'employee_id': 'A001', 'position': '质检员', 'phone': '13800138001', 'line_id': 1},
        {'name': '李四', 'employee_id': 'A002', 'position': '操作员', 'phone': '13800138002', 'line_id': 1},
        {'name': '王五', 'employee_id': 'B001', 'position': '维修员', 'phone': '13800138003', 'line_id': 2},
        {'name': '赵六', 'employee_id': 'B002', 'position': '质检员', 'phone': '13800138004', 'line_id': 2},
        {'name': '钱七', 'employee_id': 'C001', 'position': '操作员', 'phone': '13800138005', 'line_id': 3},
    ]
    
    for assistant_data in assistants_data:
        assistant = Assistant(
            name=assistant_data['name'],
            employee_id=assistant_data['employee_id'],
            position=assistant_data['position'],
            phone=assistant_data['phone'],
            production_line_id=assistant_data['line_id']
        )
        db.session.add(assistant)
    
    db.session.commit()
    print('示例数据创建完成！')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
