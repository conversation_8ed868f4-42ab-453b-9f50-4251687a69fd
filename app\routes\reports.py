from flask import Blueprint, render_template, request, jsonify
from datetime import datetime, date, timedelta
from app.models.models import db, ProductionLine, AttendanceRecord, DailyStatistics
from sqlalchemy import func, and_, or_

reports = Blueprint('reports', __name__)

@reports.route('/')
def index():
    """统计报表首页"""
    # 默认查询最近30天
    end_date = date.today()
    start_date = end_date - timedelta(days=29)
    
    # 获取生产线列表
    production_lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.name
    ).all()
    
    return render_template('reports/index.html',
                         production_lines=production_lines,
                         default_start_date=start_date.isoformat(),
                         default_end_date=end_date.isoformat())

@reports.route('/api/statistics')
def api_statistics():
    """获取统计数据API"""
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    line_id = request.args.get('line_id', type=int)
    
    # 默认查询最近30天
    if not start_date_str or not end_date_str:
        end_date = date.today()
        start_date = end_date - timedelta(days=29)
    else:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '日期格式错误'})
    
    # 构建查询
    query = AttendanceRecord.query.filter(
        AttendanceRecord.date >= start_date,
        AttendanceRecord.date <= end_date
    )
    
    if line_id:
        query = query.filter(AttendanceRecord.production_line_id == line_id)
    
    # 总体统计
    total_stats = query.with_entities(
        func.sum(AttendanceRecord.regular_employees).label('total_regular'),
        func.sum(AttendanceRecord.assistant_employees).label('total_assistant'),
        func.sum(AttendanceRecord.total_employees).label('total_all'),
        func.avg(AttendanceRecord.total_employees).label('avg_all'),
        func.max(AttendanceRecord.total_employees).label('max_all'),
        func.min(AttendanceRecord.total_employees).label('min_all'),
        func.count(AttendanceRecord.id).label('record_count')
    ).first()
    
    # 按日期统计
    daily_stats = query.with_entities(
        AttendanceRecord.date,
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total')
    ).group_by(AttendanceRecord.date).order_by(AttendanceRecord.date).all()
    
    # 按生产线统计
    line_stats = db.session.query(
        ProductionLine.id,
        ProductionLine.name,
        func.sum(AttendanceRecord.regular_employees).label('total_regular'),
        func.sum(AttendanceRecord.assistant_employees).label('total_assistant'),
        func.sum(AttendanceRecord.total_employees).label('total_all'),
        func.avg(AttendanceRecord.total_employees).label('avg_all'),
        func.count(AttendanceRecord.id).label('record_count')
    ).join(
        AttendanceRecord, ProductionLine.id == AttendanceRecord.production_line_id
    ).filter(
        AttendanceRecord.date >= start_date,
        AttendanceRecord.date <= end_date,
        ProductionLine.is_active == True
    )
    
    if line_id:
        line_stats = line_stats.filter(ProductionLine.id == line_id)
    
    line_stats = line_stats.group_by(
        ProductionLine.id, ProductionLine.name
    ).order_by(ProductionLine.name).all()
    
    # 准备返回数据
    result = {
        'success': True,
        'period': {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'days': (end_date - start_date).days + 1
        },
        'total': {
            'regular_employees': total_stats.total_regular or 0,
            'assistant_employees': total_stats.total_assistant or 0,
            'total_employees': total_stats.total_all or 0,
            'avg_employees': round(total_stats.avg_all or 0, 1),
            'max_employees': total_stats.max_all or 0,
            'min_employees': total_stats.min_all or 0,
            'record_count': total_stats.record_count or 0
        },
        'daily': [
            {
                'date': stat.date.isoformat(),
                'regular_employees': stat.regular,
                'assistant_employees': stat.assistant,
                'total_employees': stat.total
            } for stat in daily_stats
        ],
        'by_line': [
            {
                'line_id': stat.id,
                'line_name': stat.name,
                'regular_employees': stat.total_regular,
                'assistant_employees': stat.total_assistant,
                'total_employees': stat.total_all,
                'avg_employees': round(stat.avg_all or 0, 1),
                'record_count': stat.record_count
            } for stat in line_stats
        ]
    }
    
    return jsonify(result)

@reports.route('/api/trend')
def api_trend():
    """获取趋势数据API"""
    days = request.args.get('days', 30, type=int)
    line_id = request.args.get('line_id', type=int)
    
    # 计算日期范围
    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)
    
    # 构建查询
    query = AttendanceRecord.query.filter(
        AttendanceRecord.date >= start_date,
        AttendanceRecord.date <= end_date
    )
    
    if line_id:
        query = query.filter(AttendanceRecord.production_line_id == line_id)
    
    # 按日期分组统计
    daily_data = query.with_entities(
        AttendanceRecord.date,
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total')
    ).group_by(AttendanceRecord.date).order_by(AttendanceRecord.date).all()
    
    # 创建完整的日期序列
    date_range = []
    current_date = start_date
    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)
    
    # 填充数据
    trend_data = []
    daily_dict = {stat.date: stat for stat in daily_data}
    
    for target_date in date_range:
        if target_date in daily_dict:
            stat = daily_dict[target_date]
            trend_data.append({
                'date': target_date.isoformat(),
                'regular_employees': stat.regular,
                'assistant_employees': stat.assistant,
                'total_employees': stat.total
            })
        else:
            trend_data.append({
                'date': target_date.isoformat(),
                'regular_employees': 0,
                'assistant_employees': 0,
                'total_employees': 0
            })
    
    return jsonify({
        'success': True,
        'data': trend_data
    })

@reports.route('/api/comparison')
def api_comparison():
    """获取对比数据API"""
    # 本月和上月对比
    today = date.today()
    
    # 本月
    current_month_start = today.replace(day=1)
    current_month_end = today
    
    # 上月
    if current_month_start.month == 1:
        last_month_start = current_month_start.replace(year=current_month_start.year-1, month=12)
    else:
        last_month_start = current_month_start.replace(month=current_month_start.month-1)
    
    last_month_end = current_month_start - timedelta(days=1)
    
    # 本月统计
    current_stats = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total'),
        func.avg(AttendanceRecord.total_employees).label('avg'),
        func.count(AttendanceRecord.id).label('count')
    ).filter(
        AttendanceRecord.date >= current_month_start,
        AttendanceRecord.date <= current_month_end
    ).first()
    
    # 上月统计
    last_stats = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total'),
        func.avg(AttendanceRecord.total_employees).label('avg'),
        func.count(AttendanceRecord.id).label('count')
    ).filter(
        AttendanceRecord.date >= last_month_start,
        AttendanceRecord.date <= last_month_end
    ).first()
    
    return jsonify({
        'success': True,
        'current_month': {
            'period': f"{current_month_start.isoformat()} 至 {current_month_end.isoformat()}",
            'regular_employees': current_stats.regular or 0,
            'assistant_employees': current_stats.assistant or 0,
            'total_employees': current_stats.total or 0,
            'avg_employees': round(current_stats.avg or 0, 1),
            'record_count': current_stats.count or 0
        },
        'last_month': {
            'period': f"{last_month_start.isoformat()} 至 {last_month_end.isoformat()}",
            'regular_employees': last_stats.regular or 0,
            'assistant_employees': last_stats.assistant or 0,
            'total_employees': last_stats.total or 0,
            'avg_employees': round(last_stats.avg or 0, 1),
            'record_count': last_stats.count or 0
        }
    })
