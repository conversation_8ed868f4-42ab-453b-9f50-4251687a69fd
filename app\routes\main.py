from flask import Blueprint, render_template, jsonify
from datetime import datetime, timedelta
from sqlalchemy import func
from app.models.models import db, ProductionLine, AttendanceRecord, DailyStatistics

main = Blueprint('main', __name__)

@main.route('/')
def index():
    """首页"""
    # 获取今日统计数据
    today = datetime.now().date()
    
    # 今日出勤统计
    today_records = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('total_regular'),
        func.sum(AttendanceRecord.assistant_employees).label('total_assistant'),
        func.sum(AttendanceRecord.total_employees).label('total_all'),
        func.count(AttendanceRecord.id).label('active_lines')
    ).filter(AttendanceRecord.date == today).first()
    
    # 总生产线数
    total_lines = ProductionLine.query.filter_by(is_active=True).count()
    
    # 近7天出勤趋势
    week_ago = today - timedelta(days=6)
    week_records = db.session.query(
        AttendanceRecord.date,
        func.sum(AttendanceRecord.total_employees).label('total')
    ).filter(
        AttendanceRecord.date >= week_ago,
        AttendanceRecord.date <= today
    ).group_by(AttendanceRecord.date).order_by(AttendanceRecord.date).all()
    
    # 各生产线今日出勤情况
    line_records = db.session.query(
        ProductionLine.name,
        AttendanceRecord.regular_employees,
        AttendanceRecord.assistant_employees,
        AttendanceRecord.total_employees
    ).join(
        AttendanceRecord, ProductionLine.id == AttendanceRecord.production_line_id
    ).filter(
        AttendanceRecord.date == today,
        ProductionLine.is_active == True
    ).all()
    
    # 准备统计数据
    stats = {
        'today_total': today_records.total_all or 0,
        'today_regular': today_records.total_regular or 0,
        'today_assistant': today_records.total_assistant or 0,
        'active_lines': today_records.active_lines or 0,
        'total_lines': total_lines
    }
    
    # 准备图表数据
    chart_data = {
        'dates': [],
        'attendance': []
    }
    
    # 填充近7天的数据
    for i in range(7):
        date = week_ago + timedelta(days=i)
        chart_data['dates'].append(date.strftime('%m-%d'))
        
        # 查找对应日期的出勤数据
        found = False
        for record in week_records:
            if record.date == date:
                chart_data['attendance'].append(record.total)
                found = True
                break
        if not found:
            chart_data['attendance'].append(0)
    
    return render_template('index.html', 
                         stats=stats, 
                         chart_data=chart_data,
                         line_records=line_records,
                         today=today)

@main.route('/dashboard-data')
def dashboard_data():
    """获取仪表板数据API"""
    today = datetime.now().date()
    
    # 今日统计
    today_stats = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total'),
        func.count(AttendanceRecord.id).label('lines')
    ).filter(AttendanceRecord.date == today).first()
    
    # 近30天趋势
    month_ago = today - timedelta(days=29)
    month_records = db.session.query(
        AttendanceRecord.date,
        func.sum(AttendanceRecord.total_employees).label('total')
    ).filter(
        AttendanceRecord.date >= month_ago,
        AttendanceRecord.date <= today
    ).group_by(AttendanceRecord.date).order_by(AttendanceRecord.date).all()
    
    # 各生产线统计
    line_stats = db.session.query(
        ProductionLine.name,
        func.avg(AttendanceRecord.total_employees).label('avg_attendance'),
        func.max(AttendanceRecord.total_employees).label('max_attendance'),
        func.count(AttendanceRecord.id).label('record_count')
    ).join(
        AttendanceRecord, ProductionLine.id == AttendanceRecord.production_line_id
    ).filter(
        AttendanceRecord.date >= month_ago,
        ProductionLine.is_active == True
    ).group_by(ProductionLine.id, ProductionLine.name).all()
    
    return jsonify({
        'today': {
            'regular': today_stats.regular or 0,
            'assistant': today_stats.assistant or 0,
            'total': today_stats.total or 0,
            'active_lines': today_stats.lines or 0
        },
        'trend': [
            {
                'date': record.date.isoformat(),
                'total': record.total
            } for record in month_records
        ],
        'lines': [
            {
                'name': stat.name,
                'avg_attendance': round(stat.avg_attendance or 0, 1),
                'max_attendance': stat.max_attendance or 0,
                'record_count': stat.record_count
            } for stat in line_stats
        ]
    })
