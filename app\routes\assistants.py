from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from datetime import datetime
from app.models.models import db, ProductionLine, Assistant

assistants = Blueprint('assistants', __name__)

@assistants.route('/production-line/<int:line_id>/assistants')
def index(line_id):
    """辅助人员列表"""
    line = ProductionLine.query.get_or_404(line_id)
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    assistants_query = Assistant.query.filter_by(
        production_line_id=line_id, is_active=True
    ).order_by(Assistant.created_at.desc())
    
    assistants_page = assistants_query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('assistants/index.html',
                         line=line,
                         assistants=assistants_page)

@assistants.route('/production-line/<int:line_id>/assistants/add', methods=['GET', 'POST'])
def add(line_id):
    """添加辅助人员"""
    line = ProductionLine.query.get_or_404(line_id)
    
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        employee_id = data.get('employee_id', '').strip()
        position = data.get('position', '').strip()
        phone = data.get('phone', '').strip()
        
        # 验证数据
        if not name:
            if request.is_json:
                return jsonify({'success': False, 'message': '姓名不能为空'})
            flash('姓名不能为空', 'error')
            return render_template('assistants/add.html', line=line)
        
        # 检查工号是否重复
        if employee_id:
            existing = Assistant.query.filter_by(
                employee_id=employee_id, is_active=True
            ).first()
            if existing:
                if request.is_json:
                    return jsonify({'success': False, 'message': '工号已存在'})
                flash('工号已存在', 'error')
                return render_template('assistants/add.html', line=line)
        
        # 创建辅助人员
        assistant = Assistant(
            name=name,
            employee_id=employee_id if employee_id else None,
            position=position if position else None,
            phone=phone if phone else None,
            production_line_id=line_id
        )
        
        db.session.add(assistant)
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True,
                'message': '辅助人员添加成功',
                'data': assistant.to_dict()
            })
        
        flash('辅助人员添加成功', 'success')
        return redirect(url_for('assistants.index', line_id=line_id))
    
    return render_template('assistants/add.html', line=line)

@assistants.route('/assistants/<int:assistant_id>/edit', methods=['GET', 'POST'])
def edit(assistant_id):
    """编辑辅助人员"""
    assistant = Assistant.query.get_or_404(assistant_id)
    line = assistant.production_line
    
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        employee_id = data.get('employee_id', '').strip()
        position = data.get('position', '').strip()
        phone = data.get('phone', '').strip()
        
        # 验证数据
        if not name:
            if request.is_json:
                return jsonify({'success': False, 'message': '姓名不能为空'})
            flash('姓名不能为空', 'error')
            return render_template('assistants/edit.html', assistant=assistant, line=line)
        
        # 检查工号是否重复（排除自己）
        if employee_id:
            existing = Assistant.query.filter(
                Assistant.employee_id == employee_id,
                Assistant.id != assistant_id,
                Assistant.is_active == True
            ).first()
            if existing:
                if request.is_json:
                    return jsonify({'success': False, 'message': '工号已存在'})
                flash('工号已存在', 'error')
                return render_template('assistants/edit.html', assistant=assistant, line=line)
        
        # 更新辅助人员
        assistant.name = name
        assistant.employee_id = employee_id if employee_id else None
        assistant.position = position if position else None
        assistant.phone = phone if phone else None
        
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True,
                'message': '辅助人员更新成功',
                'data': assistant.to_dict()
            })
        
        flash('辅助人员更新成功', 'success')
        return redirect(url_for('assistants.index', line_id=line.id))
    
    return render_template('assistants/edit.html', assistant=assistant, line=line)

@assistants.route('/assistants/<int:assistant_id>/delete', methods=['POST'])
def delete(assistant_id):
    """删除辅助人员（软删除）"""
    assistant = Assistant.query.get_or_404(assistant_id)
    line_id = assistant.production_line_id
    
    # 软删除
    assistant.is_active = False
    db.session.commit()
    
    if request.is_json:
        return jsonify({'success': True, 'message': '辅助人员删除成功'})
    
    flash('辅助人员删除成功', 'success')
    return redirect(url_for('assistants.index', line_id=line_id))

@assistants.route('/api/production-line/<int:line_id>/assistants')
def api_list(line_id):
    """获取生产线辅助人员列表API"""
    assistants_list = Assistant.query.filter_by(
        production_line_id=line_id, is_active=True
    ).order_by(Assistant.name).all()
    
    return jsonify({
        'success': True,
        'data': [assistant.to_dict() for assistant in assistants_list]
    })

@assistants.route('/api/assistants/<int:assistant_id>')
def api_detail(assistant_id):
    """获取辅助人员详情API"""
    assistant = Assistant.query.get_or_404(assistant_id)
    
    return jsonify({
        'success': True,
        'data': assistant.to_dict()
    })
