{% extends "base.html" %}

{% block title %}录入出勤 - 生产线出勤管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">录入出勤数据</h4>
            </div>
            <div class="card-body">
                <form id="addAttendanceForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="production_line_id" class="form-label">生产线 <span class="text-danger">*</span></label>
                            <select class="form-select" id="production_line_id" name="production_line_id" required>
                                <option value="">请选择生产线</option>
                                {% for line in production_lines %}
                                <option value="{{ line.id }}">{{ line.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                请选择生产线
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="date" name="date" required 
                                   value="{{ date.today().isoformat() }}" max="{{ date.today().isoformat() }}">
                            <div class="invalid-feedback">
                                请选择日期
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="regular_employees" class="form-label">正式员工人数</label>
                            <input type="number" class="form-control" id="regular_employees" name="regular_employees" 
                                   min="0" max="999" value="0" onchange="updateTotal()">
                            <div class="invalid-feedback">
                                请输入有效的人数（0-999）
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="assistant_employees" class="form-label">辅助人员人数</label>
                            <input type="number" class="form-control" id="assistant_employees" name="assistant_employees" 
                                   min="0" max="999" value="0" onchange="updateTotal()">
                            <div class="invalid-feedback">
                                请输入有效的人数（0-999）
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">总出勤人数</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-users"></i>
                            </span>
                            <input type="text" class="form-control bg-light" id="total_display" readonly value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="请输入备注信息（可选）"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('attendance.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 快速录入提示 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb text-warning"></i> 录入提示
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>每个生产线每天只能录入一条出勤记录</li>
                    <li>如需修改已录入的数据，请在出勤记录列表中进行编辑</li>
                    <li>总出勤人数会自动计算（正式员工 + 辅助人员）</li>
                    <li>建议每日及时录入出勤数据，以便准确统计</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateTotal() {
    const regular = parseInt(document.getElementById('regular_employees').value) || 0;
    const assistant = parseInt(document.getElementById('assistant_employees').value) || 0;
    const total = regular + assistant;
    document.getElementById('total_display').value = total;
}

document.getElementById('addAttendanceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // 表单验证
    if (!validateForm('addAttendanceForm')) {
        return;
    }
    
    // 验证人数
    const regularInput = document.getElementById('regular_employees');
    const assistantInput = document.getElementById('assistant_employees');
    
    if (!validateNumber(regularInput) || !validateNumber(assistantInput)) {
        return;
    }
    
    showLoading(submitBtn);
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{{ url_for("attendance.add") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("attendance.index") }}';
        } else {
            alert('保存失败：' + data.message);
            hideLoading(submitBtn, originalText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败，请重试');
        hideLoading(submitBtn, originalText);
    });
});

// 实时验证
document.getElementById('production_line_id').addEventListener('change', function() {
    if (this.value) {
        this.classList.remove('is-invalid');
    }
});

document.getElementById('date').addEventListener('change', function() {
    if (this.value) {
        this.classList.remove('is-invalid');
    }
});

document.getElementById('regular_employees').addEventListener('input', function() {
    validateNumber(this);
    updateTotal();
});

document.getElementById('assistant_employees').addEventListener('input', function() {
    validateNumber(this);
    updateTotal();
});

// 初始化总数显示
updateTotal();
</script>
{% endblock %}
