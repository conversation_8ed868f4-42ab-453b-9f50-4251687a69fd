{% extends "base.html" %}

{% block title %}编辑生产线 - {{ line.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">编辑生产线</h4>
            </div>
            <div class="card-body">
                <form id="editLineForm" method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">生产线名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="请输入生产线名称" maxlength="100" value="{{ line.name }}">
                        <div class="invalid-feedback">
                            请输入生产线名称
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="请输入生产线描述（可选）">{{ line.description or '' }}</textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('production_lines.detail', line_id=line.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('editLineForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // 表单验证
    if (!validateForm('editLineForm')) {
        return;
    }
    
    showLoading(submitBtn);
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{{ url_for("production_lines.edit", line_id=line.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("production_lines.detail", line_id=line.id) }}';
        } else {
            alert('保存失败：' + data.message);
            hideLoading(submitBtn, originalText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败，请重试');
        hideLoading(submitBtn, originalText);
    });
});

// 实时验证
document.getElementById('name').addEventListener('input', function() {
    if (this.value.trim()) {
        this.classList.remove('is-invalid');
    }
});
</script>
{% endblock %}
