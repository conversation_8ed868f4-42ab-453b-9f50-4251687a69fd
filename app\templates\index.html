{% extends "base.html" %}

{% block title %}首页 - 生产线出勤管理系统{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-3">生产线出勤管理系统</h1>
        <p class="text-muted">今日：{{ today.strftime('%Y年%m月%d日') }}</p>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.today_total }}</div>
                <div class="stats-label">今日总出勤</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.today_regular }}</div>
                <div class="stats-label">正式员工</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.today_assistant }}</div>
                <div class="stats-label">辅助人员</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.active_lines }}/{{ stats.total_lines }}</div>
                <div class="stats-label">活跃生产线</div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和表格 -->
<div class="row">
    <!-- 出勤趋势图 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">近7天出勤趋势</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="attendanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 今日各生产线出勤 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">今日各线出勤</h5>
            </div>
            <div class="card-body">
                {% if line_records %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>生产线</th>
                                    <th>正式</th>
                                    <th>辅助</th>
                                    <th>总计</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in line_records %}
                                <tr>
                                    <td>{{ record.name }}</td>
                                    <td>{{ record.regular_employees }}</td>
                                    <td>{{ record.assistant_employees }}</td>
                                    <td><strong>{{ record.total_employees }}</strong></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <p>今日暂无出勤记录</p>
                        <a href="{{ url_for('attendance.index') }}" class="btn btn-primary btn-sm">
                            录入出勤数据
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">快捷操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('attendance.add') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus"></i> 录入出勤
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('production_lines.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-industry"></i> 管理生产线
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('reports.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-bar"></i> 查看报表
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('attendance.index') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list"></i> 出勤记录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 创建出勤趋势图
    const chartData = {
        labels: {{ chart_data.dates | tojson }},
        datasets: [{
            label: '出勤人数',
            data: {{ chart_data.attendance | tojson }},
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };
    
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: false
            },
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    };
    
    createChart('attendanceChart', 'line', chartData, chartOptions);
});
</script>
{% endblock %}
