from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from config import config

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)

    # 注册蓝图
    from app.routes.main import main as main_blueprint
    app.register_blueprint(main_blueprint)

    from app.routes.production_lines import production_lines as production_lines_blueprint
    app.register_blueprint(production_lines_blueprint, url_prefix='/production-lines')

    from app.routes.assistants import assistants as assistants_blueprint
    app.register_blueprint(assistants_blueprint)

    from app.routes.attendance import attendance as attendance_blueprint
    app.register_blueprint(attendance_blueprint, url_prefix='/attendance')

    from app.routes.reports import reports as reports_blueprint
    app.register_blueprint(reports_blueprint, url_prefix='/reports')

    from app.routes.api import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api')

    # 导入模型
    from app.models import models

    return app
