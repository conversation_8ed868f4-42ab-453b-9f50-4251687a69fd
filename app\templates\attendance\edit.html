{% extends "base.html" %}

{% block title %}编辑出勤记录 - {{ record.production_line.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">编辑出勤记录</h4>
                <small class="text-muted">{{ record.production_line.name }} - {{ record.formatted_date }}</small>
            </div>
            <div class="card-body">
                <form id="editAttendanceForm" method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">生产线</label>
                            <input type="text" class="form-control bg-light" readonly 
                                   value="{{ record.production_line.name }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">日期</label>
                            <input type="text" class="form-control bg-light" readonly 
                                   value="{{ record.formatted_date }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="regular_employees" class="form-label">正式员工人数</label>
                            <input type="number" class="form-control" id="regular_employees" name="regular_employees" 
                                   min="0" max="999" value="{{ record.regular_employees }}" onchange="updateTotal()">
                            <div class="invalid-feedback">
                                请输入有效的人数（0-999）
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="assistant_employees" class="form-label">辅助人员人数</label>
                            <input type="number" class="form-control" id="assistant_employees" name="assistant_employees" 
                                   min="0" max="999" value="{{ record.assistant_employees }}" onchange="updateTotal()">
                            <div class="invalid-feedback">
                                请输入有效的人数（0-999）
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">总出勤人数</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-users"></i>
                            </span>
                            <input type="text" class="form-control bg-light" id="total_display" readonly 
                                   value="{{ record.total_employees }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="请输入备注信息（可选）">{{ record.notes or '' }}</textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('attendance.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save"></i> 保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateTotal() {
    const regular = parseInt(document.getElementById('regular_employees').value) || 0;
    const assistant = parseInt(document.getElementById('assistant_employees').value) || 0;
    const total = regular + assistant;
    document.getElementById('total_display').value = total;
}

document.getElementById('editAttendanceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // 验证人数
    const regularInput = document.getElementById('regular_employees');
    const assistantInput = document.getElementById('assistant_employees');
    
    if (!validateNumber(regularInput) || !validateNumber(assistantInput)) {
        return;
    }
    
    showLoading(submitBtn);
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{{ url_for("attendance.edit", record_id=record.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("attendance.index") }}';
        } else {
            alert('保存失败：' + data.message);
            hideLoading(submitBtn, originalText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('保存失败，请重试');
        hideLoading(submitBtn, originalText);
    });
});

// 实时验证
document.getElementById('regular_employees').addEventListener('input', function() {
    validateNumber(this);
    updateTotal();
});

document.getElementById('assistant_employees').addEventListener('input', function() {
    validateNumber(this);
    updateTotal();
});

// 初始化总数显示
updateTotal();
</script>
{% endblock %}
