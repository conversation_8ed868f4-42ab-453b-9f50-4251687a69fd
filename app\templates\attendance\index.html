{% extends "base.html" %}

{% block title %}出勤记录 - 生产线出勤管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">出勤记录</h1>
    <a href="{{ url_for('attendance.add') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 录入出勤
    </a>
</div>

<!-- 筛选条件 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="line_id" class="form-label">生产线</label>
                <select class="form-select" id="line_id" name="line_id">
                    <option value="">全部生产线</option>
                    {% for line in production_lines %}
                    <option value="{{ line.id }}" {% if current_filters.line_id == line.id %}selected{% endif %}>
                        {{ line.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ current_filters.start_date or '' }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ current_filters.end_date or '' }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <a href="{{ url_for('attendance.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 清除
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 出勤记录列表 -->
{% if records.items %}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">出勤记录列表</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>生产线</th>
                        <th>正式员工</th>
                        <th>辅助人员</th>
                        <th>总计</th>
                        <th>备注</th>
                        <th>录入时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in records.items %}
                    <tr>
                        <td>{{ record.formatted_date }}</td>
                        <td>
                            <span class="badge bg-primary">{{ record.production_line.name }}</span>
                        </td>
                        <td>{{ record.regular_employees }}</td>
                        <td>{{ record.assistant_employees }}</td>
                        <td><strong>{{ record.total_employees }}</strong></td>
                        <td>
                            {% if record.notes %}
                                <span class="text-muted">{{ record.notes[:50] }}{% if record.notes|length > 50 %}...{% endif %}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ record.created_at.strftime('%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('attendance.edit', record_id=record.id) }}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteRecord({{ record.id }}, '{{ record.production_line.name }}', '{{ record.formatted_date }}')" 
                                        title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if records.pages > 1 %}
<nav aria-label="出勤记录分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if records.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('attendance.index', page=records.prev_num, **current_filters) }}">上一页</a>
        </li>
        {% endif %}
        
        {% for page_num in records.iter_pages() %}
            {% if page_num %}
                {% if page_num != records.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('attendance.index', page=page_num, **current_filters) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if records.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('attendance.index', page=records.next_num, **current_filters) }}">下一页</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-calendar-check fa-3x text-muted"></i>
    </div>
    <h4 class="text-muted">暂无出勤记录</h4>
    <p class="text-muted">点击上方按钮录入第一条出勤记录</p>
    <a href="{{ url_for('attendance.add') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 录入出勤
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function deleteRecord(recordId, lineName, date) {
    if (confirmDelete(`确定要删除${lineName}在${date}的出勤记录吗？`)) {
        fetch(`{{ url_for('attendance.delete', record_id=0) }}`.replace('0', recordId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    }
}
</script>
{% endblock %}
