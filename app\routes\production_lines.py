from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from datetime import datetime
from app.models.models import db, Production<PERSON><PERSON>, Assistant, AttendanceRecord
from sqlalchemy import func

production_lines = Blueprint('production_lines', __name__)

@production_lines.route('/')
def index():
    """生产线列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 10
    
    # 获取生产线列表
    lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.created_at.desc()
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取每个生产线的统计信息
    line_stats = {}
    for line in lines.items:
        # 辅助人员数量
        assistant_count = Assistant.query.filter_by(
            production_line_id=line.id, is_active=True
        ).count()
        
        # 最近7天平均出勤
        from datetime import timedelta
        recent_avg = db.session.query(
            func.avg(AttendanceRecord.total_employees)
        ).filter(
            AttendanceRecord.production_line_id == line.id,
            AttendanceRecord.date >= datetime.now().date() - timedelta(days=7)
        ).scalar()
        
        line_stats[line.id] = {
            'assistant_count': assistant_count,
            'recent_avg': round(recent_avg or 0, 1)
        }
    
    return render_template('production_lines/index.html', 
                         lines=lines, 
                         line_stats=line_stats)

@production_lines.route('/add', methods=['GET', 'POST'])
def add():
    """添加生产线"""
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        # 验证数据
        if not name:
            if request.is_json:
                return jsonify({'success': False, 'message': '生产线名称不能为空'})
            flash('生产线名称不能为空', 'error')
            return render_template('production_lines/add.html')
        
        # 检查名称是否重复
        existing = ProductionLine.query.filter_by(name=name, is_active=True).first()
        if existing:
            if request.is_json:
                return jsonify({'success': False, 'message': '生产线名称已存在'})
            flash('生产线名称已存在', 'error')
            return render_template('production_lines/add.html')
        
        # 创建生产线
        line = ProductionLine(name=name, description=description)
        db.session.add(line)
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True, 
                'message': '生产线创建成功',
                'data': line.to_dict()
            })
        
        flash('生产线创建成功', 'success')
        return redirect(url_for('production_lines.index'))
    
    return render_template('production_lines/add.html')

@production_lines.route('/<int:line_id>')
def detail(line_id):
    """生产线详情"""
    line = ProductionLine.query.get_or_404(line_id)
    
    # 获取辅助人员列表
    assistants = Assistant.query.filter_by(
        production_line_id=line_id, is_active=True
    ).order_by(Assistant.created_at.desc()).all()
    
    # 获取最近的出勤记录
    recent_records = AttendanceRecord.query.filter_by(
        production_line_id=line_id
    ).order_by(AttendanceRecord.date.desc()).limit(10).all()
    
    # 统计信息
    stats = {
        'assistant_count': len(assistants),
        'total_records': AttendanceRecord.query.filter_by(production_line_id=line_id).count(),
        'avg_attendance': db.session.query(
            func.avg(AttendanceRecord.total_employees)
        ).filter(AttendanceRecord.production_line_id == line_id).scalar() or 0
    }
    
    return render_template('production_lines/detail.html',
                         line=line,
                         assistants=assistants,
                         recent_records=recent_records,
                         stats=stats)

@production_lines.route('/<int:line_id>/edit', methods=['GET', 'POST'])
def edit(line_id):
    """编辑生产线"""
    line = ProductionLine.query.get_or_404(line_id)
    
    if request.method == 'POST':
        data = request.get_json() if request.is_json else request.form
        
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        
        # 验证数据
        if not name:
            if request.is_json:
                return jsonify({'success': False, 'message': '生产线名称不能为空'})
            flash('生产线名称不能为空', 'error')
            return render_template('production_lines/edit.html', line=line)
        
        # 检查名称是否重复（排除自己）
        existing = ProductionLine.query.filter(
            ProductionLine.name == name,
            ProductionLine.id != line_id,
            ProductionLine.is_active == True
        ).first()
        if existing:
            if request.is_json:
                return jsonify({'success': False, 'message': '生产线名称已存在'})
            flash('生产线名称已存在', 'error')
            return render_template('production_lines/edit.html', line=line)
        
        # 更新生产线
        line.name = name
        line.description = description
        line.updated_at = datetime.utcnow()
        db.session.commit()
        
        if request.is_json:
            return jsonify({
                'success': True,
                'message': '生产线更新成功',
                'data': line.to_dict()
            })
        
        flash('生产线更新成功', 'success')
        return redirect(url_for('production_lines.detail', line_id=line_id))
    
    return render_template('production_lines/edit.html', line=line)

@production_lines.route('/<int:line_id>/delete', methods=['POST'])
def delete(line_id):
    """删除生产线（软删除）"""
    line = ProductionLine.query.get_or_404(line_id)
    
    # 软删除
    line.is_active = False
    line.updated_at = datetime.utcnow()
    
    # 同时软删除相关的辅助人员
    Assistant.query.filter_by(production_line_id=line_id).update({'is_active': False})
    
    db.session.commit()
    
    if request.is_json:
        return jsonify({'success': True, 'message': '生产线删除成功'})
    
    flash('生产线删除成功', 'success')
    return redirect(url_for('production_lines.index'))

@production_lines.route('/api/list')
def api_list():
    """获取生产线列表API"""
    lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.name
    ).all()
    
    return jsonify({
        'success': True,
        'data': [line.to_dict() for line in lines]
    })
