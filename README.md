# 生产线出勤管理系统

一个基于Flask框架开发的制造业生产线出勤管理系统，提供生产线管理、出勤记录、统计报表和数据可视化功能。

## 功能特性

### 核心功能
- **生产线管理**：创建、编辑和删除生产线，支持自定义名称和描述
- **辅助人员管理**：为每个生产线添加、编辑和删除辅助人员信息
- **出勤记录管理**：按日期录入出勤数据，区分正式员工和辅助人员
- **统计功能**：实时显示总出勤人数，支持按生产线和日期范围查询
- **数据可视化**：使用Chart.js展示出勤趋势图表（折线图、柱状图）

### 技术特性
- 基于Flask Web框架
- 使用SQLite轻量级数据库
- SQLAlchemy ORM数据库操作
- Bootstrap 5响应式前端界面
- Chart.js数据可视化
- RESTful API接口设计
- 完整的表单验证和错误处理

## 系统要求

- Python 3.7+
- 现代Web浏览器（Chrome、Firefox、Safari、Edge）

## 快速开始

### Windows用户

1. 下载项目文件到本地目录
2. 双击运行 `start.bat` 文件
3. 等待自动安装依赖和初始化数据库
4. 浏览器访问 http://localhost:5000

### Linux/macOS用户

1. 下载项目文件到本地目录
2. 在终端中执行：
   ```bash
   chmod +x start.sh
   ./start.sh
   ```
3. 浏览器访问 http://localhost:5000

### 手动安装

1. 创建虚拟环境：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   # 或
   venv\Scripts\activate.bat  # Windows
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 初始化数据库：
   ```bash
   export FLASK_APP=run.py  # Linux/macOS
   # 或
   set FLASK_APP=run.py  # Windows
   
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

4. 创建示例数据：
   ```bash
   flask deploy
   ```

5. 启动应用：
   ```bash
   python run.py
   ```

## 使用说明

### 1. 生产线管理
- 在"生产线管理"页面可以添加新的生产线
- 每个生产线可以设置名称和描述
- 支持编辑和删除（软删除）生产线

### 2. 辅助人员管理
- 在生产线详情页面可以管理该生产线的辅助人员
- 可以添加人员的姓名、工号、职位、联系电话等信息
- 支持编辑和删除辅助人员信息

### 3. 出勤记录
- 在"出勤管理"页面录入每日出勤数据
- 需要选择生产线、日期，输入正式员工和辅助人员人数
- 系统会自动计算总出勤人数
- 每个生产线每天只能录入一条记录

### 4. 统计报表
- 在"统计报表"页面查看出勤统计数据
- 支持按生产线、日期范围筛选
- 提供折线图和柱状图两种图表类型
- 可以导出统计数据为CSV格式

### 5. 首页仪表板
- 显示今日出勤概况
- 展示近7天出勤趋势
- 提供各生产线今日出勤情况
- 快捷操作入口

## 项目结构

```
生产线出勤管理系统/
├── app/                    # 应用程序包
│   ├── __init__.py        # 应用工厂
│   ├── models/            # 数据库模型
│   │   ├── __init__.py
│   │   └── models.py      # 数据模型定义
│   ├── routes/            # 路由蓝图
│   │   ├── __init__.py
│   │   ├── main.py        # 主页路由
│   │   ├── production_lines.py  # 生产线路由
│   │   ├── assistants.py  # 辅助人员路由
│   │   ├── attendance.py  # 出勤记录路由
│   │   ├── reports.py     # 统计报表路由
│   │   └── api.py         # API路由
│   ├── templates/         # HTML模板
│   │   ├── base.html      # 基础模板
│   │   ├── index.html     # 首页模板
│   │   ├── production_lines/  # 生产线模板
│   │   ├── attendance/    # 出勤管理模板
│   │   └── reports/       # 报表模板
│   └── static/            # 静态文件
│       ├── css/           # 样式文件
│       └── js/            # JavaScript文件
├── migrations/            # 数据库迁移文件
├── config.py             # 配置文件
├── run.py               # 应用启动文件
├── requirements.txt     # Python依赖
├── start.bat           # Windows启动脚本
├── start.sh            # Linux/macOS启动脚本
├── .env                # 环境变量
└── README.md           # 说明文档
```

## API接口

系统提供RESTful API接口，主要端点包括：

- `GET /api/health` - 健康检查
- `GET /api/dashboard` - 仪表板数据
- `GET /api/production-lines` - 生产线列表
- `GET /api/attendance/today` - 今日出勤情况
- `GET /api/statistics/summary` - 统计摘要
- `GET /api/export/attendance` - 导出出勤数据

## 数据库设计

### 主要数据表
- `production_lines` - 生产线信息
- `assistants` - 辅助人员信息
- `attendance_records` - 出勤记录
- `daily_statistics` - 每日统计汇总

### 关系设计
- 一个生产线可以有多个辅助人员
- 一个生产线可以有多条出勤记录
- 出勤记录按日期和生产线唯一约束

## 配置说明

### 环境变量
在 `.env` 文件中配置：
```
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///attendance.db
```

### 配置类
- `DevelopmentConfig` - 开发环境配置
- `ProductionConfig` - 生产环境配置
- `TestingConfig` - 测试环境配置

## 部署建议

### 生产环境部署
1. 使用Gunicorn作为WSGI服务器
2. 配置Nginx作为反向代理
3. 使用PostgreSQL或MySQL替代SQLite
4. 设置环境变量 `FLASK_ENV=production`
5. 配置日志记录和监控

### 安全建议
1. 修改默认的SECRET_KEY
2. 使用HTTPS协议
3. 定期备份数据库
4. 限制文件上传大小
5. 实施访问控制和用户认证

## 故障排除

### 常见问题
1. **端口占用**：修改run.py中的端口号
2. **数据库错误**：删除数据库文件重新初始化
3. **依赖安装失败**：检查Python版本和网络连接
4. **页面显示异常**：清除浏览器缓存

### 日志查看
- 开发环境：控制台输出
- 生产环境：logs/attendance.log文件

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/attendance-system

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础的生产线管理功能
- 实现出勤记录管理功能
- 实现统计报表功能
- 实现数据可视化功能
