{% extends "base.html" %}

{% block title %}生产线管理 - 生产线出勤管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">生产线管理</h1>
    <a href="{{ url_for('production_lines.add') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 添加生产线
    </a>
</div>

{% if lines.items %}
<div class="row">
    {% for line in lines.items %}
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card production-line-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ line.name }}</h5>
                <div class="btn-group btn-group-sm">
                    <a href="{{ url_for('production_lines.detail', line_id=line.id) }}" 
                       class="btn btn-outline-primary" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="{{ url_for('production_lines.edit', line_id=line.id) }}" 
                       class="btn btn-outline-secondary" title="编辑">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button type="button" class="btn btn-outline-danger" 
                            onclick="deleteLine({{ line.id }}, '{{ line.name }}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if line.description %}
                <p class="card-text text-muted">{{ line.description }}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <div class="h4 text-primary mb-0">{{ line_stats[line.id].assistant_count }}</div>
                            <small class="text-muted">辅助人员</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success mb-0">{{ line_stats[line.id].recent_avg }}</div>
                        <small class="text-muted">近7天平均</small>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small>创建时间：{{ line.created_at.strftime('%Y-%m-%d') }}</small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 分页 -->
{% if lines.pages > 1 %}
<nav aria-label="生产线分页">
    <ul class="pagination justify-content-center">
        {% if lines.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('production_lines.index', page=lines.prev_num) }}">上一页</a>
        </li>
        {% endif %}
        
        {% for page_num in lines.iter_pages() %}
            {% if page_num %}
                {% if page_num != lines.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('production_lines.index', page=page_num) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">…</span>
            </li>
            {% endif %}
        {% endfor %}
        
        {% if lines.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('production_lines.index', page=lines.next_num) }}">下一页</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="text-center py-5">
    <div class="mb-4">
        <i class="fas fa-industry fa-3x text-muted"></i>
    </div>
    <h4 class="text-muted">暂无生产线</h4>
    <p class="text-muted">点击上方按钮添加第一条生产线</p>
    <a href="{{ url_for('production_lines.add') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 添加生产线
    </a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function deleteLine(lineId, lineName) {
    if (confirmDelete(`确定要删除生产线"${lineName}"吗？删除后相关的出勤记录将无法关联到该生产线。`)) {
        fetch(`{{ url_for('production_lines.delete', line_id=0) }}`.replace('0', lineId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    }
}
</script>
{% endblock %}
