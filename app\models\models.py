from datetime import datetime
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class ProductionLine(db.Model):
    """生产线模型"""
    __tablename__ = 'production_lines'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # 关联关系
    assistants = db.relationship('Assistant', backref='production_line', lazy=True, cascade='all, delete-orphan')
    attendance_records = db.relationship('AttendanceRecord', backref='production_line', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<ProductionLine {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'assistant_count': len(self.assistants)
        }

class Assistant(db.Model):
    """辅助人员模型"""
    __tablename__ = 'assistants'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    employee_id = db.Column(db.String(20), unique=True)
    position = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    production_line_id = db.Column(db.Integer, db.ForeignKey('production_lines.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def __repr__(self):
        return f'<Assistant {self.name}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'employee_id': self.employee_id,
            'position': self.position,
            'phone': self.phone,
            'production_line_id': self.production_line_id,
            'production_line_name': self.production_line.name if self.production_line else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_active': self.is_active
        }

class AttendanceRecord(db.Model):
    """出勤记录模型"""
    __tablename__ = 'attendance_records'
    
    id = db.Column(db.Integer, primary_key=True)
    production_line_id = db.Column(db.Integer, db.ForeignKey('production_lines.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    regular_employees = db.Column(db.Integer, default=0)  # 正式员工出勤人数
    assistant_employees = db.Column(db.Integer, default=0)  # 辅助人员出勤人数
    total_employees = db.Column(db.Integer, default=0)  # 总出勤人数
    notes = db.Column(db.Text)  # 备注
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.String(50))  # 记录创建者
    
    # 复合唯一约束：每个生产线每天只能有一条记录
    __table_args__ = (db.UniqueConstraint('production_line_id', 'date', name='unique_line_date'),)
    
    def __repr__(self):
        return f'<AttendanceRecord {self.production_line.name} {self.date}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'production_line_id': self.production_line_id,
            'production_line_name': self.production_line.name if self.production_line else None,
            'date': self.date.isoformat() if self.date else None,
            'regular_employees': self.regular_employees,
            'assistant_employees': self.assistant_employees,
            'total_employees': self.total_employees,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by
        }
    
    @property
    def formatted_date(self):
        """格式化日期显示"""
        return self.date.strftime('%Y年%m月%d日') if self.date else ''

class DailyStatistics(db.Model):
    """每日统计模型"""
    __tablename__ = 'daily_statistics'
    
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False, unique=True)
    total_regular_employees = db.Column(db.Integer, default=0)
    total_assistant_employees = db.Column(db.Integer, default=0)
    total_employees = db.Column(db.Integer, default=0)
    active_production_lines = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<DailyStatistics {self.date}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'total_regular_employees': self.total_regular_employees,
            'total_assistant_employees': self.total_assistant_employees,
            'total_employees': self.total_employees,
            'active_production_lines': self.active_production_lines,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @property
    def formatted_date(self):
        """格式化日期显示"""
        return self.date.strftime('%Y年%m月%d日') if self.date else ''
