#!/bin/bash

echo "启动生产线出勤管理系统..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo "安装依赖包..."
pip install -r requirements.txt

# 初始化数据库
echo "初始化数据库..."
export FLASK_APP=run.py
flask db init
flask db migrate -m "Initial migration"
flask db upgrade

# 创建示例数据
echo "创建示例数据..."
flask deploy

# 启动应用
echo "启动应用服务器..."
echo "应用将在 http://localhost:5000 启动"
echo "按 Ctrl+C 停止服务器"
python run.py
