import unittest
import os
import tempfile
from datetime import date, datetime
from app import create_app, db
from app.models.models import Production<PERSON><PERSON>, Assistant, AttendanceRecord

class BasicTestCase(unittest.TestCase):
    """基础测试用例"""
    
    def setUp(self):
        """测试前准备"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        self.app = create_app('testing')
        self.app.config['DATABASE_URL'] = f'sqlite:///{self.db_path}'
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
    
    def tearDown(self):
        """测试后清理"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def test_app_exists(self):
        """测试应用是否存在"""
        self.assertIsNotNone(self.app)
    
    def test_app_is_testing(self):
        """测试应用是否在测试模式"""
        self.assertTrue(self.app.config['TESTING'])
    
    def test_home_page(self):
        """测试首页"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'生产线出勤管理系统', response.data)
    
    def test_production_line_model(self):
        """测试生产线模型"""
        line = ProductionLine(name='测试生产线', description='测试描述')
        db.session.add(line)
        db.session.commit()
        
        self.assertEqual(line.name, '测试生产线')
        self.assertEqual(line.description, '测试描述')
        self.assertTrue(line.is_active)
        self.assertIsNotNone(line.created_at)
    
    def test_assistant_model(self):
        """测试辅助人员模型"""
        # 先创建生产线
        line = ProductionLine(name='测试生产线')
        db.session.add(line)
        db.session.commit()
        
        # 创建辅助人员
        assistant = Assistant(
            name='张三',
            employee_id='A001',
            position='质检员',
            production_line_id=line.id
        )
        db.session.add(assistant)
        db.session.commit()
        
        self.assertEqual(assistant.name, '张三')
        self.assertEqual(assistant.employee_id, 'A001')
        self.assertEqual(assistant.position, '质检员')
        self.assertEqual(assistant.production_line_id, line.id)
        self.assertTrue(assistant.is_active)
    
    def test_attendance_record_model(self):
        """测试出勤记录模型"""
        # 先创建生产线
        line = ProductionLine(name='测试生产线')
        db.session.add(line)
        db.session.commit()
        
        # 创建出勤记录
        record = AttendanceRecord(
            production_line_id=line.id,
            date=date.today(),
            regular_employees=10,
            assistant_employees=5,
            total_employees=15,
            notes='测试记录'
        )
        db.session.add(record)
        db.session.commit()
        
        self.assertEqual(record.production_line_id, line.id)
        self.assertEqual(record.date, date.today())
        self.assertEqual(record.regular_employees, 10)
        self.assertEqual(record.assistant_employees, 5)
        self.assertEqual(record.total_employees, 15)
        self.assertEqual(record.notes, '测试记录')
    
    def test_production_line_api(self):
        """测试生产线API"""
        # 创建生产线
        response = self.client.post('/production-lines/add', 
                                  json={'name': '测试生产线', 'description': '测试描述'})
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])
        
        # 获取生产线列表
        response = self.client.get('/api/production-lines')
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']), 1)
        self.assertEqual(data['data'][0]['name'], '测试生产线')
    
    def test_attendance_api(self):
        """测试出勤API"""
        # 先创建生产线
        line = ProductionLine(name='测试生产线')
        db.session.add(line)
        db.session.commit()
        
        # 创建出勤记录
        response = self.client.post('/attendance/add', json={
            'production_line_id': line.id,
            'date': date.today().isoformat(),
            'regular_employees': 10,
            'assistant_employees': 5,
            'notes': '测试记录'
        })
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])
        
        # 获取今日出勤
        response = self.client.get('/api/attendance/today')
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])
        self.assertEqual(len(data['data']), 1)
        self.assertEqual(data['data'][0]['total_employees'], 15)
    
    def test_dashboard_api(self):
        """测试仪表板API"""
        response = self.client.get('/api/dashboard')
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        self.assertTrue(data['success'])
        self.assertIn('today', data['data'])
        self.assertIn('week_trend', data['data'])
        self.assertIn('line_attendance', data['data'])

if __name__ == '__main__':
    unittest.main()
