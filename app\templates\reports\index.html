{% extends "base.html" %}

{% block title %}统计报表 - 生产线出勤管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3">统计报表</h1>
    <button type="button" class="btn btn-outline-primary" onclick="exportData()">
        <i class="fas fa-download"></i> 导出数据
    </button>
</div>

<!-- 查询条件 -->
<div class="card mb-4">
    <div class="card-body">
        <form id="queryForm" class="row g-3">
            <div class="col-md-3">
                <label for="line_id" class="form-label">生产线</label>
                <select class="form-select" id="line_id" name="line_id">
                    <option value="">全部生产线</option>
                    {% for line in production_lines %}
                    <option value="{{ line.id }}">{{ line.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="start_date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="{{ default_start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="{{ default_end_date }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="fas fa-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4" id="statsOverview">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="totalEmployees">-</div>
                <div class="stats-label">总出勤人次</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="avgEmployees">-</div>
                <div class="stats-label">平均出勤</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="maxEmployees">-</div>
                <div class="stats-label">最高出勤</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="recordCount">-</div>
                <div class="stats-label">记录数量</div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 出勤趋势图 -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">出勤趋势图</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="chartType" id="lineChart" value="line" checked>
                    <label class="btn btn-outline-primary" for="lineChart">折线图</label>
                    
                    <input type="radio" class="btn-check" name="chartType" id="barChart" value="bar">
                    <label class="btn btn-outline-primary" for="barChart">柱状图</label>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 生产线对比 -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">生产线对比</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="lineComparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细数据表格 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">详细统计数据</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="detailTable">
                <thead>
                    <tr>
                        <th>生产线</th>
                        <th>正式员工</th>
                        <th>辅助人员</th>
                        <th>总出勤</th>
                        <th>平均出勤</th>
                        <th>记录数</th>
                    </tr>
                </thead>
                <tbody id="detailTableBody">
                    <tr>
                        <td colspan="6" class="text-center text-muted">请点击查询按钮获取数据</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let trendChart = null;
let comparisonChart = null;
let currentData = null;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化查询
    queryStatistics();
    
    // 图表类型切换
    document.querySelectorAll('input[name="chartType"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (currentData) {
                updateTrendChart(currentData.daily, this.value);
            }
        });
    });
});

document.getElementById('queryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    queryStatistics();
});

function queryStatistics() {
    const formData = new FormData(document.getElementById('queryForm'));
    const params = new URLSearchParams(formData);
    
    fetch(`{{ url_for('reports.api_statistics') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentData = data;
                updateStatistics(data);
                updateTrendChart(data.daily, document.querySelector('input[name="chartType"]:checked').value);
                updateComparisonChart(data.by_line);
                updateDetailTable(data.by_line);
            } else {
                alert('查询失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('查询失败，请重试');
        });
}

function updateStatistics(data) {
    document.getElementById('totalEmployees').textContent = data.total.total_employees;
    document.getElementById('avgEmployees').textContent = data.total.avg_employees;
    document.getElementById('maxEmployees').textContent = data.total.max_employees;
    document.getElementById('recordCount').textContent = data.total.record_count;
}

function updateTrendChart(dailyData, chartType) {
    const ctx = document.getElementById('trendChart');
    
    if (trendChart) {
        trendChart.destroy();
    }
    
    const chartData = {
        labels: dailyData.map(item => {
            const date = new Date(item.date);
            return `${date.getMonth() + 1}-${date.getDate()}`;
        }),
        datasets: [{
            label: '总出勤',
            data: dailyData.map(item => item.total_employees),
            borderColor: '#667eea',
            backgroundColor: chartType === 'bar' ? 'rgba(102, 126, 234, 0.8)' : 'rgba(102, 126, 234, 0.1)',
            borderWidth: 2,
            fill: chartType === 'line'
        }, {
            label: '正式员工',
            data: dailyData.map(item => item.regular_employees),
            borderColor: '#f093fb',
            backgroundColor: chartType === 'bar' ? 'rgba(240, 147, 251, 0.8)' : 'rgba(240, 147, 251, 0.1)',
            borderWidth: 2,
            fill: false
        }, {
            label: '辅助人员',
            data: dailyData.map(item => item.assistant_employees),
            borderColor: '#4facfe',
            backgroundColor: chartType === 'bar' ? 'rgba(79, 172, 254, 0.8)' : 'rgba(79, 172, 254, 0.1)',
            borderWidth: 2,
            fill: false
        }]
    };
    
    trendChart = createChart('trendChart', chartType, chartData, {
        title: '出勤趋势'
    });
}

function updateComparisonChart(lineData) {
    const ctx = document.getElementById('lineComparisonChart');
    
    if (comparisonChart) {
        comparisonChart.destroy();
    }
    
    const chartData = {
        labels: lineData.map(item => item.line_name),
        datasets: [{
            data: lineData.map(item => item.total_employees),
            backgroundColor: [
                '#667eea', '#f093fb', '#4facfe', '#43e97b', '#ffa726',
                '#ef5350', '#ab47bc', '#26c6da', '#66bb6a', '#ffca28'
            ]
        }]
    };
    
    comparisonChart = createChart('lineComparisonChart', 'doughnut', chartData, {
        title: '生产线出勤对比',
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    });
}

function updateDetailTable(lineData) {
    const tbody = document.getElementById('detailTableBody');
    
    if (lineData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = lineData.map(item => `
        <tr>
            <td><span class="badge bg-primary">${item.line_name}</span></td>
            <td>${item.regular_employees}</td>
            <td>${item.assistant_employees}</td>
            <td><strong>${item.total_employees}</strong></td>
            <td>${item.avg_employees}</td>
            <td>${item.record_count}</td>
        </tr>
    `).join('');
}

function resetForm() {
    document.getElementById('line_id').value = '';
    document.getElementById('start_date').value = '{{ default_start_date }}';
    document.getElementById('end_date').value = '{{ default_end_date }}';
    queryStatistics();
}

function exportData() {
    const formData = new FormData(document.getElementById('queryForm'));
    const params = new URLSearchParams(formData);
    
    fetch(`{{ url_for('api.export_attendance') }}?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 准备CSV数据
                const headers = ['日期', '生产线', '正式员工', '辅助人员', '总出勤', '备注', '录入人'];
                const csvData = [headers];
                
                data.data.forEach(record => {
                    csvData.push([
                        record.date,
                        record.line_name,
                        record.regular_employees,
                        record.assistant_employees,
                        record.total_employees,
                        record.notes,
                        record.created_by
                    ]);
                });
                
                const filename = `出勤数据_${data.period.start_date}_${data.period.end_date}.csv`;
                exportToCSV(csvData, filename);
            } else {
                alert('导出失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('导出失败，请重试');
        });
}
</script>
{% endblock %}
