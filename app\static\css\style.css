/* 生产线出勤管理系统样式 */

body {
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.production-line-card {
    transition: transform 0.2s;
}

.production-line-card:hover {
    transform: translateY(-2px);
}

.attendance-form {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.navbar-brand {
    font-weight: bold;
}

.alert {
    border-radius: 0.5rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

.text-primary {
    color: #667eea !important;
}

.bg-primary {
    background-color: #667eea !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
