from flask import Blueprint, jsonify, request
from datetime import datetime, date, timedelta
from app.models.models import db, ProductionLine, Assistant, AttendanceRecord, DailyStatistics
from sqlalchemy import func

api = Blueprint('api', __name__)

@api.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

@api.route('/dashboard')
def dashboard():
    """仪表板数据"""
    today = date.today()
    
    # 今日统计
    today_stats = db.session.query(
        func.sum(AttendanceRecord.regular_employees).label('regular'),
        func.sum(AttendanceRecord.assistant_employees).label('assistant'),
        func.sum(AttendanceRecord.total_employees).label('total'),
        func.count(AttendanceRecord.id).label('active_lines')
    ).filter(AttendanceRecord.date == today).first()
    
    # 总生产线数
    total_lines = ProductionLine.query.filter_by(is_active=True).count()
    
    # 近7天趋势
    week_ago = today - timedelta(days=6)
    week_trend = db.session.query(
        AttendanceRecord.date,
        func.sum(AttendanceRecord.total_employees).label('total')
    ).filter(
        AttendanceRecord.date >= week_ago,
        AttendanceRecord.date <= today
    ).group_by(AttendanceRecord.date).order_by(AttendanceRecord.date).all()
    
    # 各生产线今日出勤
    line_attendance = db.session.query(
        ProductionLine.id,
        ProductionLine.name,
        AttendanceRecord.regular_employees,
        AttendanceRecord.assistant_employees,
        AttendanceRecord.total_employees
    ).join(
        AttendanceRecord, ProductionLine.id == AttendanceRecord.production_line_id
    ).filter(
        AttendanceRecord.date == today,
        ProductionLine.is_active == True
    ).all()
    
    return jsonify({
        'success': True,
        'data': {
            'today': {
                'regular_employees': today_stats.regular or 0,
                'assistant_employees': today_stats.assistant or 0,
                'total_employees': today_stats.total or 0,
                'active_lines': today_stats.active_lines or 0,
                'total_lines': total_lines
            },
            'week_trend': [
                {
                    'date': record.date.isoformat(),
                    'total_employees': record.total
                } for record in week_trend
            ],
            'line_attendance': [
                {
                    'line_id': record.id,
                    'line_name': record.name,
                    'regular_employees': record.regular_employees,
                    'assistant_employees': record.assistant_employees,
                    'total_employees': record.total_employees
                } for record in line_attendance
            ]
        }
    })

@api.route('/production-lines')
def production_lines():
    """生产线列表"""
    lines = ProductionLine.query.filter_by(is_active=True).order_by(
        ProductionLine.name
    ).all()
    
    return jsonify({
        'success': True,
        'data': [line.to_dict() for line in lines]
    })

@api.route('/production-lines/<int:line_id>')
def production_line_detail(line_id):
    """生产线详情"""
    line = ProductionLine.query.get_or_404(line_id)
    
    # 获取辅助人员
    assistants = Assistant.query.filter_by(
        production_line_id=line_id, is_active=True
    ).all()
    
    # 获取最近出勤记录
    recent_records = AttendanceRecord.query.filter_by(
        production_line_id=line_id
    ).order_by(AttendanceRecord.date.desc()).limit(10).all()
    
    return jsonify({
        'success': True,
        'data': {
            'line': line.to_dict(),
            'assistants': [assistant.to_dict() for assistant in assistants],
            'recent_records': [record.to_dict() for record in recent_records]
        }
    })

@api.route('/attendance/today')
def today_attendance():
    """今日出勤情况"""
    today = date.today()
    
    records = db.session.query(
        ProductionLine.id,
        ProductionLine.name,
        AttendanceRecord.regular_employees,
        AttendanceRecord.assistant_employees,
        AttendanceRecord.total_employees,
        AttendanceRecord.notes
    ).join(
        AttendanceRecord, ProductionLine.id == AttendanceRecord.production_line_id
    ).filter(
        AttendanceRecord.date == today,
        ProductionLine.is_active == True
    ).order_by(ProductionLine.name).all()
    
    return jsonify({
        'success': True,
        'date': today.isoformat(),
        'data': [
            {
                'line_id': record.id,
                'line_name': record.name,
                'regular_employees': record.regular_employees,
                'assistant_employees': record.assistant_employees,
                'total_employees': record.total_employees,
                'notes': record.notes
            } for record in records
        ]
    })

@api.route('/statistics/summary')
def statistics_summary():
    """统计摘要"""
    # 今日
    today = date.today()
    today_stats = db.session.query(
        func.sum(AttendanceRecord.total_employees).label('total')
    ).filter(AttendanceRecord.date == today).scalar() or 0
    
    # 昨日
    yesterday = today - timedelta(days=1)
    yesterday_stats = db.session.query(
        func.sum(AttendanceRecord.total_employees).label('total')
    ).filter(AttendanceRecord.date == yesterday).scalar() or 0
    
    # 本月
    month_start = today.replace(day=1)
    month_stats = db.session.query(
        func.sum(AttendanceRecord.total_employees).label('total'),
        func.avg(AttendanceRecord.total_employees).label('avg')
    ).filter(AttendanceRecord.date >= month_start).first()
    
    # 活跃生产线
    active_lines = ProductionLine.query.filter_by(is_active=True).count()
    
    # 今日有出勤记录的生产线
    today_active_lines = db.session.query(
        func.count(AttendanceRecord.id)
    ).filter(AttendanceRecord.date == today).scalar() or 0
    
    return jsonify({
        'success': True,
        'data': {
            'today': {
                'total_employees': today_stats,
                'active_lines': today_active_lines,
                'vs_yesterday': today_stats - yesterday_stats
            },
            'month': {
                'total_employees': month_stats.total or 0,
                'avg_employees': round(month_stats.avg or 0, 1)
            },
            'system': {
                'total_lines': active_lines,
                'today_coverage': round((today_active_lines / active_lines * 100) if active_lines > 0 else 0, 1)
            }
        }
    })

@api.route('/export/attendance')
def export_attendance():
    """导出出勤数据"""
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    line_id = request.args.get('line_id', type=int)
    
    # 默认导出最近30天
    if not start_date_str or not end_date_str:
        end_date = date.today()
        start_date = end_date - timedelta(days=29)
    else:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'success': False, 'message': '日期格式错误'})
    
    # 构建查询
    query = db.session.query(
        AttendanceRecord.date,
        ProductionLine.name.label('line_name'),
        AttendanceRecord.regular_employees,
        AttendanceRecord.assistant_employees,
        AttendanceRecord.total_employees,
        AttendanceRecord.notes,
        AttendanceRecord.created_by
    ).join(
        ProductionLine, AttendanceRecord.production_line_id == ProductionLine.id
    ).filter(
        AttendanceRecord.date >= start_date,
        AttendanceRecord.date <= end_date
    )
    
    if line_id:
        query = query.filter(AttendanceRecord.production_line_id == line_id)
    
    records = query.order_by(
        AttendanceRecord.date.desc(),
        ProductionLine.name
    ).all()
    
    # 准备导出数据
    export_data = []
    for record in records:
        export_data.append({
            'date': record.date.isoformat(),
            'line_name': record.line_name,
            'regular_employees': record.regular_employees,
            'assistant_employees': record.assistant_employees,
            'total_employees': record.total_employees,
            'notes': record.notes or '',
            'created_by': record.created_by or ''
        })
    
    return jsonify({
        'success': True,
        'period': {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        },
        'data': export_data
    })
