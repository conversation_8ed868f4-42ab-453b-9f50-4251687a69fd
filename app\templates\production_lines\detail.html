{% extends "base.html" %}

{% block title %}{{ line.name }} - 生产线详情{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3">{{ line.name }}</h1>
        <p class="text-muted">{{ line.description or '暂无描述' }}</p>
    </div>
    <div class="btn-group">
        <a href="{{ url_for('production_lines.edit', line_id=line.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i> 编辑
        </a>
        <a href="{{ url_for('production_lines.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.assistant_count }}</div>
                <div class="stats-label">辅助人员</div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ stats.total_records }}</div>
                <div class="stats-label">出勤记录</div>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number">{{ "%.1f"|format(stats.avg_attendance) }}</div>
                <div class="stats-label">平均出勤</div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 辅助人员列表 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">辅助人员</h5>
                <a href="{{ url_for('assistants.add', line_id=line.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> 添加
                </a>
            </div>
            <div class="card-body">
                {% if assistants %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>姓名</th>
                                <th>工号</th>
                                <th>职位</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for assistant in assistants %}
                            <tr>
                                <td>{{ assistant.name }}</td>
                                <td>{{ assistant.employee_id or '-' }}</td>
                                <td>{{ assistant.position or '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('assistants.edit', assistant_id=assistant.id) }}" 
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteAssistant({{ assistant.id }}, '{{ assistant.name }}')" 
                                                title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <p>暂无辅助人员</p>
                    <a href="{{ url_for('assistants.add', line_id=line.id) }}" class="btn btn-sm btn-primary">
                        添加第一个辅助人员
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最近出勤记录 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">最近出勤记录</h5>
                <a href="{{ url_for('attendance.add') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> 录入
                </a>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>正式</th>
                                <th>辅助</th>
                                <th>总计</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.date.strftime('%m-%d') }}</td>
                                <td>{{ record.regular_employees }}</td>
                                <td>{{ record.assistant_employees }}</td>
                                <td><strong>{{ record.total_employees }}</strong></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-2">
                    <a href="{{ url_for('attendance.index', line_id=line.id) }}" class="btn btn-sm btn-outline-primary">
                        查看全部记录
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <p>暂无出勤记录</p>
                    <a href="{{ url_for('attendance.add') }}" class="btn btn-sm btn-primary">
                        录入第一条记录
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteAssistant(assistantId, assistantName) {
    if (confirmDelete(`确定要删除辅助人员"${assistantName}"吗？`)) {
        fetch(`{{ url_for('assistants.delete', assistant_id=0) }}`.replace('0', assistantId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    }
}
</script>
{% endblock %}
